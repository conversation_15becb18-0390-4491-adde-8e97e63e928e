import 'dart:async';
import 'package:click_bazaar/core/function/functions.dart';
import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:click_bazaar/core/theme/app_text_styles.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/models/pavilion_model.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/page/rastalar_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/widgets/universal_loading.dart';
import '../../../../di/dependency_injection.dart' as di;
import '../../../../translations/locale_keys.g.dart';
import '../bloc/naz_pavilion/naz_pavilion_bloc.dart';
import '../widgets/pavilion_block_item.dart';

class NazPavilionPage extends StatefulWidget {
  final String pavilionId;
  final String pavilionTitle;

  const NazPavilionPage({
    super.key,
    required this.pavilionId,
    required this.pavilionTitle,
  });

  @override
  State<NazPavilionPage> createState() => _NazPavilionPageState();
}

class _NazPavilionPageState extends State<NazPavilionPage> {
  // Store reference to the bloc to use it safely
  NazPavilionBloc? _nazPavilionBloc;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        _nazPavilionBloc = di.di<NazPavilionBloc>()..add(LoadBlocks(widget.pavilionId));
        return _nazPavilionBloc!;
      },
      child: Scaffold(
        backgroundColor: AppColors.cBackgroundColor,
        appBar: AppBar(
          backgroundColor: AppColors.cBackgroundColor,
          elevation: 0,
          centerTitle: false,
          title: Text(
            widget.pavilionTitle,
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          actions: [],
        ),
        body: Column(
          children: [
            Expanded(
              child: BlocConsumer<NazPavilionBloc, NazPavilionState>(
                listener: (context, state) {
                  // Handle refresh errors with toast messages
                  // Show toast when there's a refresh error and existing data
                  if (state.hasRefreshError &&
                      state.hasAnyBlocks &&
                      state.message != null) {
                    // This is a refresh error - show toast while keeping existing content
                    final message = state.isNetworkError
                        ? LocaleKeys.common_no_internet_connection.tr()
                        : state.message!;

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(message),
                        backgroundColor: AppColors.cReddishColor,
                        duration: const Duration(seconds: 3),
                        action: state.isNetworkError
                            ? SnackBarAction(
                          label: LocaleKeys.common_retry.tr(),
                          textColor: AppColors.white,
                          onPressed: () {
                            context.read<NazPavilionBloc>().add(
                              RefreshBlocks(widget.pavilionId),
                            );
                          },
                        )
                            : null,
                      ),
                    );
                  }
                },
                builder: (context, state) {
                  return RefreshIndicator(
                    onRefresh: () => _handleRefresh(context),
                    color: AppColors.cFirstColor,
                    backgroundColor: AppColors.cCardsColor,
                    strokeWidth: 2.0,
                    displacement: 40.0,
                    child: _buildContent(context, state),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Handle refresh action
  Future<void> _handleRefresh(BuildContext context) async {
    // Provide haptic feedback for better user experience
    HapticFeedback.lightImpact();

    // Add a small delay to show the refresh indicator
    await Future.delayed(const Duration(milliseconds: 300));
    context.read<NazPavilionBloc>().add(RefreshBlocks(widget.pavilionId));
  }

  Widget _buildContent(BuildContext context, NazPavilionState state) {
    if (state.isLoading || state.isInitial || state.isRefreshing) {
      return _buildShimmerLoading();
    }

    // Handle initial load errors - show centered error widget only if no existing data
    if (state.isFailure && !state.hasAnyBlocks) {
      return _buildErrorState(context, state);
    }

    if (state.isSuccess || state.hasAnyBlocks) {
      return _buildBlocksList(state.blocks);
    }
    return _buildEmptyState();
  }

  Widget _buildBlocksList(List<BlockData> blocks) {
    if (blocks.isEmpty) {
      return _buildEmptyState();
    }
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      itemCount: blocks.length,
      physics: const AlwaysScrollableScrollPhysics(
        parent: BouncingScrollPhysics(),
      ),
      itemBuilder: (context, index) {
        final blockData = blocks[index];
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          child: PavilionBlockItem(
            blockData: blockData,
            onClick: () {
              _onBlockTap(blockData);
            },
          ),
        );
      },
    );
  }

  void _onBlockTap(BlockData blockData) async {
    // Navigate and await the result
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RastalarPage(
          blockId: blockData.id,
          pavilionId: widget.pavilionId,
          blockName: blockData.title,
          rastaCount: rastalarCount(blockData),
        ),
      ),
    );

    // Check if widget is still mounted and refresh using the stored bloc reference
    print("called");
    print(mounted);
    if (mounted && _nazPavilionBloc != null) {
      // Use the stored bloc reference instead of context.read
      _nazPavilionBloc!.add(RefreshBlocks(widget.pavilionId));
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64.w,
            color: AppColors.cTextGrayColor,
          ),
          Gap(16.h),
          Text(
            LocaleKeys.market_structure_no_blocks.tr(),
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.cTextGrayColor,
            ),
          ),
          Gap(8.h),
          Text(
            LocaleKeys.market_structure_no_blocks_subtitle.tr(),
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.cTextGrayColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, NazPavilionState state) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.9,
      child: Center(
        child: UniversalLoading.error(
          message: state.isNetworkError
              ? LocaleKeys.common_no_internet_connection.tr()
              : (state.message ?? LocaleKeys.dialogs_payment_status_error_occurred.tr()),
          onRetry: () {
            context.read<NazPavilionBloc>().add(LoadBlocks(widget.pavilionId));
          },
          retryButtonText: LocaleKeys.dialogs_payment_status_retry.tr(),
          icon: state.isNetworkError ? Icons.wifi_off : null,
        ),
      ),
    );
  }

  /// Build shimmer loading skeleton
  Widget _buildShimmerLoading() {
    return UniversalLoading.shimmer(
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: 6,
        itemBuilder: (context, index) {
          return _buildShimmerCard();
        },
      ),
    );
  }

  /// Build individual shimmer card skeleton
  Widget _buildShimmerCard() {
    final random = DateTime.now().millisecondsSinceEpoch % 3;
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                height: 20.h,
                width: (120 + random * 20).w,
                decoration: BoxDecoration(
                  color: AppColors.cTextGrayColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
              Container(
                height: 14.h,
                width: 60.w,
                decoration: BoxDecoration(
                  color: AppColors.cTextGrayColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4.r),
                ),
              ),
            ],
          ),
          Gap(16.h),
          // Status items
          ...List.generate(
              3,
                  (index) => Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: Container(
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              )),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // Clean up the bloc reference if it exists
    _nazPavilionBloc?.close();
    super.dispose();
  }
}