import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/app_functions.dart';
import '../../../../translations/locale_keys.g.dart';
import '../models/rastalar_model.dart';
import '../bloc/rastalar_api/rastalar_api_bloc.dart';
import '../dialogs/rastalar_square_dialog.dart';
import '../dialogs/rastalar_empty_square_dialog.dart';
import '../widgets/rastalar_status_legend.dart';
import '../widgets/rastalar_grid_shimmer.dart';

class RastalarPage extends StatelessWidget {
  final String? pavilionId;
  final String? blockId;
  final String? blockName;
  final int? rastaCount;

  const RastalarPage(
      {super.key,
      required this.blockId,
      required this.pavilionId,
      required this.blockName,
      required this.rastaCount});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<RastalarApiBloc>()
        ..add(LoadSquares(blockId: blockId, pavilionId: pavilionId)),
      child: _RastalarPageView(
        pavilionId: pavilionId,
        blockId: blockId,
        blockName: blockName,
        rastaCount: rastaCount,
      ),
    );
  }
}

class _RastalarPageView extends StatelessWidget {
  final String? pavilionId;
  final String? blockId;
  final String? blockName;
  final int? rastaCount;

  const _RastalarPageView({
    required this.pavilionId,
    required this.blockId,
    required this.blockName,
    required this.rastaCount,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cBackgroundColor,
      appBar: AppBar(
        centerTitle: false,
        backgroundColor: AppColors.cBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          LocaleKeys.nazoratchi_market_structure_places.tr(),
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          BlocBuilder<RastalarApiBloc, RastalarApiState>(
            builder: (context, state) {
              return IconButton(
                icon: state.isRefreshing
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: AppColors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Icon(Icons.refresh, color: AppColors.white),
                onPressed: state.isRefreshing
                    ? null
                    : () {
                        context.read<RastalarApiBloc>().add(RefreshSquares(
                            blockId: blockId, pavilionId: pavilionId));
                      },
              );
            },
          ),
        ],
      ),
      body: BlocBuilder<RastalarApiBloc, RastalarApiState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const RastalarGridShimmer();
          }
          if (state.hasError) {
            return RefreshIndicator(
              onRefresh: () async {
                context.read<RastalarApiBloc>().add(
                    RefreshSquares(blockId: blockId, pavilionId: pavilionId));
                await Future.delayed(const Duration(milliseconds: 500));
              },
              color: AppColors.cFirstColor,
              backgroundColor: AppColors.cCardsColor,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: SizedBox(
                  height: MediaQuery.of(context).size.height - 200,
                  child: _buildErrorWidget(
                      context,
                      state.message ??
                          LocaleKeys.dialogs_payment_status_error_occurred
                              .tr()),
                ),
              ),
            );
          }

          if (!state.hasData) {
            return const RastalarGridShimmer();
          }

          // Show shimmer during refresh, otherwise show data
          if (state.isRefreshing) {
            return const RastalarGridShimmer();
          }

          return RefreshIndicator(
            onRefresh: () async {
              context.read<RastalarApiBloc>().add(
                  RefreshSquares(blockId: blockId, pavilionId: pavilionId));
              // Wait for the refresh to complete
              await Future.delayed(const Duration(milliseconds: 500));
            },
            color: AppColors.cFirstColor,
            backgroundColor: AppColors.cCardsColor,
            child: _buildSuccessWidget(context, state.groupedSquares),
          );
        },
      ),
    );
  }

  /// Handle square tap - show appropriate dialog
  void _onSquareTap(BuildContext context, GroupedSquares groupedSquares) {
    AppFunctions.lightHaptic();

    // Capture the bloc reference before showing dialog
    final rastalarBloc = context.read<RastalarApiBloc>();

    // Check if this is an empty square (Status 2 = bo'sh)
    final isEmptySquare =
        groupedSquares.squares.every((square) => square.status == 2);

    if (isEmptySquare) {
      // Show empty square dialog for bo'sh rasta using API data
      showDialog(
        context: context,
        builder: (context) => RastalarEmptySquareDialog.fromGroupedSquares(
          groupedSquares: groupedSquares,
        ),
      );
    } else {
      // Show regular dialog for occupied squares (paid, unpaid, unbinded)
      showDialog(
        context: context,
        builder: (context) => RastalarSquareDialog.fromGroupedSquares(
          groupedSquares: groupedSquares,
          pavilionId: pavilionId,
          blockId: blockId,
          rastalarBloc: rastalarBloc,
        ),
      );
    }
  }

  /// Build error widget
  Widget _buildErrorWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.cReddishColor,
          ),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.dialogs_payment_status_error_occurred.tr(),
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.cTextGrayColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              context
                  .read<RastalarApiBloc>()
                  .add(LoadSquares(pavilionId: pavilionId, blockId: blockId));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.cFirstColor,
            ),
            child: Text(
              LocaleKeys.dialogs_payment_status_retry.tr(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build empty widget
  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.grid_view,
            size: 64,
            color: AppColors.cTextGrayColor,
          ),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.places_not_found.tr(),
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            LocaleKeys.places_not_found_subtitle.tr(),
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.cTextGrayColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build success widget with grid
  Widget _buildSuccessWidget(
      BuildContext context, List<GroupedSquares> groupedSquares) {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: BlocBuilder<RastalarApiBloc, RastalarApiState>(
              builder: (context, state) {
                return RastalarStatusLegend(
                  rastalarCount: rastaCount,
                  isLoading: state.isLoading || state.isRefreshing,
                );
              },
            ),
          ),
        ),
        // Grid
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final group = groupedSquares[index];
                return _buildSquareItem(context, group);
              },
              childCount: groupedSquares.length,
            ),
          ),
        ),

        // Add some bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 16),
        ),
      ],
    );
  }

  /// Build individual square item
  Widget _buildSquareItem(BuildContext context, GroupedSquares groupedSquares) {
    Color backgroundColor;
    Color borderColor;

    // Set colors based on API status codes
    final primaryStatus = groupedSquares.primaryStatus;
    switch (primaryStatus) {
      case SquareStatus.paid: // Status 4 = to'langan
        backgroundColor = AppColors.cGreenishColor.withAlpha(50);
        borderColor = AppColors.cGreenishColor;
        break;
      case SquareStatus.unpaid: // Status 3 = qarzdor
        backgroundColor = AppColors.cReddishColor.withAlpha(50);
        borderColor = AppColors.cReddishColor;
        break;
      case SquareStatus.unbinded: // Status 1 = belgilanmagan
        backgroundColor = AppColors.cUnbindedColor.withAlpha(50);
        borderColor = AppColors.cUnbindedColor;
        break;
      case SquareStatus.available: // Status 2 = bo'sh
        backgroundColor = AppColors.cYellowishColor.withAlpha(50);
        borderColor = AppColors.cYellowishColor;
        break;
    }

    return GestureDetector(
      onTap: () => _onSquareTap(context, groupedSquares),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: borderColor, width: 1),
        ),
        child: groupedSquares.isMultiOccupant
            ? _buildMultiOccupantGrid(groupedSquares)
            : _buildSingleOccupantDisplay(groupedSquares),
      ),
    );
  }

  /// Build single occupant display
  Widget _buildSingleOccupantDisplay(GroupedSquares groupedSquares) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Flexible(
          child: Padding(
            padding: EdgeInsets.all(8.h),
            child: Text(
              '${groupedSquares.squares.first.title}',
              textAlign: TextAlign.center,
              style: AppTextStyles.bodyMedium.copyWith(
                fontSize: 26.sp,
                color: AppColors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        // const SizedBox(height: 4),
        // Text(
        //   _getStatusText(groupedSquares.primaryStatus),
        //   style: AppTextStyles.bodySmall.copyWith(
        //     color: AppColors.white.withAlpha(180),
        //     fontWeight: FontWeight.w400,
        //   ),
        // ),
      ],
    );
  }

  /// Build multi-occupant grid display
  Widget _buildMultiOccupantGrid(GroupedSquares groupedSquares) {
    final numbers = groupedSquares.squareNumbers;
    final count = numbers.length;

    // Always use sub-grid layout for multiple squares (both sequential and non-sequential)
    if (count > 1) {
      return _buildSubGrid(numbers);
    } else {
      // Single square fallback (shouldn't happen in multi-occupant, but jxust in case)
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${numbers.first}',
            style: AppTextStyles.bodyMedium.copyWith(
              fontSize: 26.sp,
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }
  }

  /// Build sub-grid layout for multiple squares (universal dynamic grid)
  Widget _buildSubGrid(List<int> numbers) {
    final count = numbers.length;

    if (count <= 1) {
      // Single item (shouldn't happen in multi-occupant, but just in case)
      return Center(
        child: _buildSubGridItem(numbers.first),
      );
    }

    // Calculate optimal grid dimensions
    final gridDimensions = _calculateGridDimensions(count);
    final rows = gridDimensions['rows']!;
    final cols = gridDimensions['cols']!;

    return _buildDynamicGrid(numbers, rows, cols);
  }

  /// Calculate optimal grid dimensions for any count
  Map<String, int> _calculateGridDimensions(int count) {
    if (count <= 2) {
      return {'rows': 1, 'cols': count}; // 1x2 for 2 items
    } else if (count <= 4) {
      return {'rows': 2, 'cols': 2}; // 2x2 for 3-4 items
    } else if (count <= 6) {
      return {'rows': 2, 'cols': 3}; // 2x3 for 5-6 items
    } else if (count <= 9) {
      return {'rows': 3, 'cols': 3}; // 3x3 for 7-9 items
    } else if (count <= 12) {
      return {'rows': 3, 'cols': 4}; // 3x4 for 10-12 items
    } else if (count <= 16) {
      return {'rows': 4, 'cols': 4}; // 4x4 for 13-16 items
    } else {
      // For very large counts, use a square-ish grid
      final sqrtValue = sqrt(count.toDouble()).ceil();
      return {'rows': sqrtValue, 'cols': sqrtValue};
    }
  }

  /// Build dynamic grid with specified rows and columns
  Widget _buildDynamicGrid(List<int> numbers, int rows, int cols) {
    final count = numbers.length;

    return Column(
      children: List.generate(rows, (rowIndex) {
        return Expanded(
          child: Row(
            children: List.generate(cols, (colIndex) {
              final itemIndex = rowIndex * cols + colIndex;

              if (itemIndex < count) {
                // Show actual square number
                return Expanded(
                  child: Container(
                    margin: EdgeInsets.only(
                      right: colIndex < cols - 1 ? 1 : 0,
                      bottom: rowIndex < rows - 1 ? 1 : 0,
                    ),
                    child: _buildSubGridItem(numbers[itemIndex]),
                  ),
                );
              } else {
                // Empty cell for remaining spaces
                return Expanded(
                  child: Container(
                    margin: EdgeInsets.only(
                      right: colIndex < cols - 1 ? 1 : 0,
                      bottom: rowIndex < rows - 1 ? 1 : 0,
                    ),
                    child: Container(), // Empty space
                  ),
                );
              }
            }),
          ),
        );
      }),
    );
  }
}

/// Build individual sub-grid item
Widget _buildSubGridItem(int number) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.black.withValues(alpha: 0.2),
      borderRadius: BorderRadius.circular(4),
      border: Border.all(
        color: Colors.white.withValues(alpha: 0.3),
        width: 0.5,
      ),
    ),
    child: Center(
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Padding(
          padding: EdgeInsets.all(8.h),
          child: Text(
            number.toString(),
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
              fontSize: 15.sp,
            ),
          ),
        ),
      ),
    ),
  );
}

/// Get status text based on API status codes
String _getStatusText(SquareStatus status) {
  switch (status) {
    case SquareStatus.paid: // Status 4 = to'langan
      return LocaleKeys.nazoratchi_tuzilma_status_paid.tr();
    case SquareStatus.unpaid: // Status 3 = qarzdor
      return LocaleKeys.nazoratchi_tuzilma_status_unpaid.tr();
    case SquareStatus.unbinded: // Status 1 = belgilanmagan
      return LocaleKeys.nazoratchi_tuzilma_status_unassigned.tr();
    case SquareStatus.available: // Status 2 = bo'sh
      return LocaleKeys.nazoratchi_tuzilma_status_empty.tr();
  }
}
