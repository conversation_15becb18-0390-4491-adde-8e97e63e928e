import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../di/dependency_injection.dart';
import '../services/payment_service.dart';
import '../models/payment_models.dart';
import 'payment_result_dialog.dart';
import '../../../../translations/locale_keys.g.dart';

/// Cash payment confirmation dialog
class CashPaymentDialog extends StatefulWidget {
  final String amount;
  final int squareNumber;
  final String paymentId; // Payment ID from creation step
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  const CashPaymentDialog({
    super.key,
    required this.amount,
    required this.squareNumber,
    required this.paymentId,
    this.onConfirm,
    this.onCancel,
  });

  @override
  State<CashPaymentDialog> createState() => _CashPaymentDialogState();
}

class _CashPaymentDialogState extends State<CashPaymentDialog> {
  final PaymentService _paymentService = di<PaymentService>();

  bool _isConfirmingPayment = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.cCardsColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 32),

            // Illustration
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: SvgPicture.asset(Assets.iconsCashMan),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              LocaleKeys.nazoratchi_payment_cash_payment_title.tr(namedArgs: {
                'amount': widget.amount,
                'formattedAmount': _formatAmount(widget.amount),
              }),
              textAlign: TextAlign.center,
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w600,
                height: 1.4,
              ),
            ),

            const SizedBox(height: 32),

            // Buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  // Confirm button
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: _isConfirmingPayment ? null : _handleConfirm,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.cGreenishColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isConfirmingPayment
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: AppColors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              LocaleKeys.payment_accept_payment.tr(),
                              style: AppTextStyles.bodyLarge.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Cancel button
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: TextButton(
                      onPressed: _isConfirmingPayment ? null : _handleCancel,
                      style: TextButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: AppColors.cTextGrayColor.withOpacity(0.3),
                          ),
                        ),
                      ),
                      child: Text(
                        LocaleKeys.dialogs_cash_payment_cancel.tr(),
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.cTextGrayColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// Handle confirm button press - confirm the already created payment
  Future<void> _handleConfirm() async {
    setState(() {
      _isConfirmingPayment = true;
    });

    try {
      // Confirm the payment using the provided payment ID
      final confirmResult =
          await _paymentService.confirmPayment(widget.paymentId);

      if (confirmResult.isSuccess) {
        // Success - close dialog and show success
        if (mounted) {
          Navigator.of(context).pop();
          widget.onConfirm?.call();
        }
      } else {
        _showErrorDialog(
            confirmResult.errorMessage ?? LocaleKeys.dialogs_cash_payment_confirm_error.tr());
      }
    } catch (e) {
      _showErrorDialog(LocaleKeys.dialogs_cash_payment_process_error.tr(namedArgs: {'error': e.toString()}));
    } finally {
      if (mounted) {
        setState(() {
          _isConfirmingPayment = false;
        });
      }
    }
  }

  /// Handle cancel button press
  void _handleCancel() {
    Navigator.of(context).pop();
    widget.onCancel?.call();
  }

  /// Show error dialog
  void _showErrorDialog(String message) {
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => PaymentResultDialog.error(
          message: message,
          onClose: () {
            Navigator.of(context).pop(); // Close error dialog
          },
        ),
      );
    }
  }

  String _formatAmount(String amount) {
    // Extract number from amount string like "1 kunlik (12 000 UZS)"
    final regex = RegExp(r'\(([^)]+)\)');
    final match = regex.firstMatch(amount);
    if (match != null) {
      return match.group(1) ?? amount;
    }
    return amount;
  }
}
