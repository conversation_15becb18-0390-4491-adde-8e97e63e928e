import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/universal_loading.dart';
import '../../../../di/dependency_injection.dart';
import '../../../../translations/locale_keys.g.dart';
import '../services/payment_service.dart';
import '../models/payment_models.dart';
import 'loading_dialog.dart';

/// Dialog for place check (Tekshirildi) functionality
class PlaceCheckDialog extends StatefulWidget {
  final String sellerId;
  final VoidCallback? onSuccess;
  final VoidCallback? onCancel;

  const PlaceCheckDialog({
    super.key,
    required this.sellerId,
    this.onSuccess,
    this.onCancel,
  });

  @override
  State<PlaceCheckDialog> createState() => _PlaceCheckDialogState();
}

class _PlaceCheckDialogState extends State<PlaceCheckDialog> {
  final PaymentService _paymentService = di<PaymentService>();

  PlaceCheckStatus _status = PlaceCheckStatus.initial;
  String? _errorMessage;
  PlaceCheckResponse? _response;
  int _retryCount = 0;
  static const int _maxRetries = 3;

  @override
  void initState() {
    super.initState();
    _checkPlace();
  }

  /// Perform place check API call
  Future<void> _checkPlace() async {
    setState(() {
      _status = PlaceCheckStatus.loading;
      _errorMessage = null;
    });

    try {
      final result = await _paymentService.checkPlace(widget.sellerId);

      if (result.isSuccess && result.data != null) {
        setState(() {
          _status = PlaceCheckStatus.success;
          _response = result.data;
        });

        // Auto-close after 3 seconds and call success callback
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            Navigator.of(context).pop();
            widget.onSuccess?.call();
          }
        });
      } else {
        setState(() {
          _status = PlaceCheckStatus.error;
          _errorMessage =
              result.errorMessage ?? LocaleKeys.dialogs_place_check_check_error.tr();
        });
      }
    } catch (e) {
      setState(() {
        _status = PlaceCheckStatus.error;
        _errorMessage = LocaleKeys.dialogs_place_check_unexpected_error.tr(namedArgs: {'error': e.toString()});
      });
    }
  }

  /// Retry place check with exponential backoff
  Future<void> _retryCheck() async {
    if (_retryCount >= _maxRetries) {
      setState(() {
        _status = PlaceCheckStatus.error;
        _errorMessage =
            LocaleKeys.dialogs_place_check_max_retries_reached.tr();
      });
      return;
    }

    _retryCount++;

    // Show loading state immediately for better UX
    setState(() {
      _status = PlaceCheckStatus.loading;
      _errorMessage = null;
    });

    // Exponential backoff: 1s, 2s, 4s
    final delay = Duration(seconds: 1 << (_retryCount - 1));
    await Future.delayed(delay);

    await _checkPlace();
  }

  /// Handle cancel action
  void _handleCancel() {
    Navigator.of(context).pop();
    widget.onCancel?.call();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _status != PlaceCheckStatus.loading,
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.cCardsColor,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildContent(),
              const SizedBox(height: 24),
              _buildActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    switch (_status) {
      case PlaceCheckStatus.initial:
      case PlaceCheckStatus.loading:
        return _buildLoadingContent();
      case PlaceCheckStatus.success:
        return _buildSuccessContent();
      case PlaceCheckStatus.error:
        return _buildErrorContent();
    }
  }

  Widget _buildLoadingContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(
          width: 60,
          height: 60,
          child: CircularProgressIndicator(
            color: AppColors.cFirstColor,
            strokeWidth: 4,
          ),
        ),
        const SizedBox(height: 20),
        Text(
          LocaleKeys.dialogs_place_check_checking.tr(),
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          LocaleKeys.dialogs_place_check_please_wait.tr(),
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.cTextGrayColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSuccessContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: const BoxDecoration(
            color: AppColors.cGreenishColor,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.check,
            color: AppColors.white,
            size: 32,
          ),
        ),
        const SizedBox(height: 20),
        Text(
          LocaleKeys.dialogs_place_check_success_message.tr(),
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        )
      ],
    );
  }

  Widget _buildErrorContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: const BoxDecoration(
            color: AppColors.cReddishColor,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.error_outline,
            color: AppColors.white,
            size: 32,
          ),
        ),
        const SizedBox(height: 20),
        Text(
          LocaleKeys.dialogs_place_check_error_occurred.tr(),
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          _errorMessage ?? LocaleKeys.dialogs_place_check_unknown_error.tr(),
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.cTextGrayColor,
          ),
          textAlign: TextAlign.center,
        ),
        if (_retryCount < _maxRetries) ...[
          const SizedBox(height: 8),
          Text(
            LocaleKeys.dialogs_place_check_attempt_count.tr(namedArgs: {'current': (_retryCount + 1).toString(), 'max': _maxRetries.toString()}),
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.cTextGrayColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildActions() {
    switch (_status) {
      case PlaceCheckStatus.initial:
      case PlaceCheckStatus.loading:
        return _buildLoadingActions();
      case PlaceCheckStatus.success:
        return _buildSuccessActions();
      case PlaceCheckStatus.error:
        return _buildErrorActions();
    }
  }

  Widget _buildLoadingActions() {
    return SizedBox(
      width: double.infinity,
      child: TextButton(
        onPressed: _status == PlaceCheckStatus.loading ? _handleCancel : null,
        style: TextButton.styleFrom(
          foregroundColor: AppColors.cTextGrayColor,
        ),
        child: Text(
          LocaleKeys.dialogs_place_check_cancel.tr(),
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.cTextGrayColor,
          ),
        ),
      ),
    );
  }

  Widget _buildSuccessActions() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _status == PlaceCheckStatus.success
            ? () {
                Navigator.of(context).pop();
                widget.onSuccess?.call();
              }
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.cGreenishColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          LocaleKeys.dialogs_place_check_close.tr(),
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorActions() {
    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: _handleCancel,
            style: TextButton.styleFrom(
              foregroundColor: AppColors.cTextGrayColor,
            ),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                LocaleKeys.dialogs_place_check_cancel.tr(),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.cTextGrayColor,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: _retryCount < _maxRetries ? _retryCheck : _handleCancel,
            style: ElevatedButton.styleFrom(
              backgroundColor: _retryCount < _maxRetries
                  ? AppColors.cFirstColor
                  : AppColors.cGrayForegroundColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                _retryCount < _maxRetries ? LocaleKeys.dialogs_place_check_retry.tr() : LocaleKeys.dialogs_place_check_close.tr(),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Status enum for place check dialog
enum PlaceCheckStatus {
  initial,
  loading,
  success,
  error,
}
