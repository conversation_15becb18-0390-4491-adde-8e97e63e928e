import 'dart:io';

import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/features/face_control/facedetectionview.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:page_transition/page_transition.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../di/dependency_injection.dart';
import '../../../../translations/locale_keys.g.dart';
import '../models/payment_models.dart';
import '../services/payment_service.dart';
import '../bloc/rastalar_api/rastalar_api_bloc.dart';
import 'qr_payment_bottomsheet.dart';

/// Payment status bottomsheet that handles the entire payment flow
class PaymentStatusBottomSheet extends StatefulWidget {
  final PaymentType paymentType;
  final int quantity;
  final int dailyRate;
  final String sellerId;
  final String day;
  final List<String> placeIds;
  final String squareNumber;
  final String? pavilionId; // Add pavilion ID for proper refresh
  final VoidCallback onSuccess;
  final VoidCallback onCancel;

  const PaymentStatusBottomSheet({
    super.key,
    required this.paymentType,
    required this.quantity,
    required this.dailyRate,
    required this.sellerId,
    required this.day,
    required this.placeIds,
    required this.squareNumber,
    this.pavilionId,
    required this.onSuccess,
    required this.onCancel,
  });

  @override
  State<PaymentStatusBottomSheet> createState() =>
      _PaymentStatusBottomSheetState();
}

class _PaymentStatusBottomSheetState extends State<PaymentStatusBottomSheet> {
  final PaymentService _paymentService = di<PaymentService>();

  PaymentStatus _status = PaymentStatus.creating;
  String? _errorMessage;
  int? _paymentId;
  String? _paymentStringId;
  bool _isPendingPayment = false; // Track if this is a pending payment (201)
  CreatePaymentResponse? _paymentData; // Store full payment data

  @override
  void initState() {
    super.initState();
    _createPayment();
  }

  /// Create payment
  Future<void> _createPayment() async {
    try {
      setState(() {
        _status = PaymentStatus.creating;
        _errorMessage = null;
      });

      final result = await _paymentService.createPayment(
        seller: widget.sellerId,
        days: widget.quantity,
        price: widget.quantity * widget.dailyRate,
        paymentType: widget.paymentType,
        places: widget.placeIds,
      );

      if (result.isSuccess && result.data != null) {
        setState(() {
          _paymentId = result.data!.paymentId;
          _paymentStringId = result.data!.id;
          _paymentData = result.data!;
          _isPendingPayment =
              result.statusCode == 201; // 201 means pending payment
          _status = PaymentStatus.awaitingConfirmation;
        });

        // For QR payments with NEW payment (200), show QR code directly
        if (widget.paymentType == PaymentType.qr && result.statusCode == 200) {
          _proceedToQRPayment();
          return;
        }

        // For QR payments with old cheques (201), show cheque details with QR button
        // This will be handled in the UI with "${LocaleKeys.dialogs_payment_status_qr_show_button.tr()}" button
      } else {
        String errorMessage =
            result.errorMessage ?? LocaleKeys.dialogs_payment_status_creation_error.tr();

        // Check for specific error about unconfirmed payment
        if (errorMessage.contains('tasdiqlanmagan to\'lov mavjud') ||
            errorMessage.contains('unconfirmed payment')) {
          errorMessage = LocaleKeys.dialogs_payment_status_unconfirmed_payment_exists.tr();
        }

        setState(() {
          _status = PaymentStatus.error;
          _errorMessage = errorMessage;
        });
      }
    } catch (e) {
      setState(() {
        _status = PaymentStatus.error;
        _errorMessage = LocaleKeys.dialogs_payment_status_creation_error_with_message.tr(namedArgs: {'error': e.toString()});
      });
    }
  }

  /// Confirm payment
  Future<void> _confirmPayment() async {
    if (Platform.isAndroid) {
      await Navigator.push(
        context,
        PageTransition(
          alignment: Alignment.center,
          type: PageTransitionType.scale,
          child: FaceRecognitionView(
            debug: EMULATOR,
            getBackAfterRecognized: true,
            afterRecognized: () async {
              if (_paymentStringId == null) return;

              try {
                setState(() {
                  _status = PaymentStatus.confirming;
                });

                final result =
                    await _paymentService.confirmPayment(_paymentStringId!);

                if (result.isSuccess && result.data?.message == LocaleKeys.dialogs_payment_status_success_status.tr()) {
                  setState(() {
                    _status = PaymentStatus.success;
                  });

                  // Trigger grid refresh and auto close after success
                  _refreshGrid();
                  Future.delayed(const Duration(seconds: 2), () {
                    if (mounted) {
                      widget.onSuccess();
                    }
                  });
                } else {
                  setState(() {
                    _status = PaymentStatus.error;
                    _errorMessage =
                        result.errorMessage ?? LocaleKeys.dialogs_payment_status_confirm_error.tr();
                  });
                }
              } catch (e) {
                setState(() {
                  _status = PaymentStatus.error;
                  _errorMessage = LocaleKeys.dialogs_payment_status_process_error.tr(namedArgs: {'error': e.toString()});
                });
              }
            },
          ),
        ),
      ).then((value) {
        ///
      });
    } else if (Platform.isIOS) {
      ///
    }
  }

  /// Proceed to QR payment (for QR payments with old cheques)
  void _proceedToQRPayment() {
    if (_paymentStringId == null) return;

    // Close current bottomsheet
    Navigator.of(context).pop();

    // Show QR payment bottomsheet
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (qrContext) => QRPaymentBottomSheet(
        amount:
            '${widget.quantity} kunlik (${widget.quantity * widget.dailyRate} UZS)',
        squareNumber: int.tryParse(widget.squareNumber) ?? 0,
        sellerId: widget.sellerId,
        day: widget.day,
        price: widget.quantity * widget.dailyRate,
        quantity: widget.quantity,
        placeIds: widget.placeIds,
        onPaymentSuccess: () {
          Navigator.of(qrContext).pop();
          widget.onSuccess();
        },
        onPaymentError: () {
          Navigator.of(qrContext).pop();
          // Return to payment selector
        },
        onClose: () {
          Navigator.of(qrContext).pop();
          // Return to payment selector
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: AppColors.cCardsColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          const SizedBox(height: 24),

          // Title
          Text(
            _getTitle(),
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: 40),

          // Content based on status
          _buildContent(),

          const SizedBox(height: 40),

          // Action buttons
          _buildActionButtons(),

          const SizedBox(height: 26),
        ],
      ),
    );
  }

  String _getTitle() {
    switch (_status) {
      case PaymentStatus.creating:
        return LocaleKeys.dialogs_payment_status_creating.tr();
      case PaymentStatus.awaitingConfirmation:
        if (_isPendingPayment) {
          return LocaleKeys.dialogs_payment_status_existing_payment_found.tr();
        } else {
          return LocaleKeys.dialogs_payment_status_payment_created.tr();
        }
      case PaymentStatus.confirming:
        return LocaleKeys.dialogs_payment_status_confirming.tr();
      case PaymentStatus.success:
        return LocaleKeys.dialogs_payment_status_paid.tr();
      case PaymentStatus.error:
        return LocaleKeys.dialogs_payment_status_error_occurred.tr();
    }
  }

  Widget _buildContent() {
    switch (_status) {
      case PaymentStatus.creating:
      case PaymentStatus.confirming:
        return Center(
          child: const SizedBox(
            width: 60,
            height: 60,
            child: CircularProgressIndicator(
              color: AppColors.cFirstColor,
              strokeWidth: 4,
            ),
          ),
        );

      case PaymentStatus.awaitingConfirmation:
        return Column(
          children: [
            Icon(
              widget.paymentType == PaymentType.cash
                  ? Icons.payments_outlined
                  : widget.paymentType == PaymentType.qr
                      ? Icons.qr_code
                      : Icons.credit_card_outlined,
              size: 60,
              color: _isPendingPayment ? Colors.orange : AppColors.cFirstColor,
            ),
            const SizedBox(height: 16),

            // Show cheque details
            if (_paymentData != null) ...[
              _buildChequeDetails(),
              const SizedBox(height: 16),
            ],

            Text(
              LocaleKeys.dialogs_payment_status_days_display.tr(namedArgs: {'count': widget.quantity.toString()}) + ' (${widget.quantity * widget.dailyRate} ${LocaleKeys.dialogs_square_dialog_currency_uzs.tr()})',
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${LocaleKeys.dialogs_payment_status_places_label.tr()} ${widget.squareNumber}',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.cTextGrayColor,
              ),
            ),

            if (_isPendingPayment) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  LocaleKeys.dialogs_payment_status_pending_payment_notice.tr(),
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.orange,
                  ),
                ),
              ),
            ],
          ],
        );

      case PaymentStatus.success:
        return SvgPicture.asset(Assets.iconsTickCircle, height: 80);

      case PaymentStatus.error:
        return Column(
          children: [
            const Icon(
              Icons.error_outline,
              size: 60,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage ?? LocaleKeys.dialogs_payment_status_unknown_error.tr(),
              textAlign: TextAlign.center,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.white,
              ),
            ),
          ],
        );
    }
  }

  Widget _buildActionButtons() {
    switch (_status) {
      case PaymentStatus.creating:
      case PaymentStatus.confirming:
        return TextButton(
          onPressed: widget.onCancel,
          child: Text(
            LocaleKeys.dialogs_payment_status_close.tr(),
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.cTextGrayColor,
            ),
          ),
        );

      case PaymentStatus.awaitingConfirmation:
        if (_isPendingPayment) {
          // For pending payments, show ignore/delete and confirm options
          return Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _deletePendingPayment,
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.red),
                      ),
                      child: Text(
                        LocaleKeys.dialogs_payment_status_delete_payment.tr(),
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: widget.paymentType == PaymentType.qr
                          ? _proceedToQRPayment
                          : _confirmPayment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.cFirstColor,
                      ),
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          widget.paymentType == PaymentType.qr
                              ? LocaleKeys.dialogs_payment_status_show_qr_code.tr()
                              : LocaleKeys.dialogs_payment_status_accept_payment.tr(),
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: widget.onCancel,
                  child: Text(
                    LocaleKeys.dialogs_payment_status_close.tr(),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.cTextGrayColor,
                    ),
                  ),
                ),
              ),
            ],
          );
        } else {
          // For new payments, show normal confirm/cancel options
          return Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: widget.onCancel,
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: AppColors.cTextGrayColor),
                  ),
                  child: Text(
                    LocaleKeys.dialogs_payment_status_close.tr(),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.cTextGrayColor,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _confirmPayment,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.cFirstColor,
                  ),
                  child: Text(
                    LocaleKeys.dialogs_payment_status_accept_payment.tr(),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          );
        }

      case PaymentStatus.success:
        return ElevatedButton(
          onPressed: widget.onSuccess,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
          ),
          child: Text(
            LocaleKeys.dialogs_payment_status_continue.tr(),
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        );

      case PaymentStatus.error:
        return Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: widget.onCancel,
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: AppColors.cTextGrayColor),
                ),
                child: Text(
                  LocaleKeys.dialogs_payment_status_close.tr(),
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.cTextGrayColor,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: _createPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.cFirstColor,
                ),
                child: Text(
                  LocaleKeys.dialogs_payment_status_retry.tr(),
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        );
    }
  }

  /// Handle deleting pending payment and return to payment selector
  Future<void> _deletePendingPayment() async {
    if (_paymentId == null) return;

    try {
      setState(() {
        _status = PaymentStatus.creating; // Show loading state
      });

      // Delete the old cheque instead of just ignoring
      final result = await _paymentService.deleteOldCheque(_paymentId!);

      if (result.isSuccess) {
        // Successfully deleted, go back to payment selector
        widget.onCancel();
      } else {
        setState(() {
          _status = PaymentStatus.error;
          _errorMessage = result.errorMessage ?? LocaleKeys.dialogs_payment_status_delete_error.tr();
        });
      }
    } catch (e) {
      setState(() {
        _status = PaymentStatus.error;
        _errorMessage = LocaleKeys.dialogs_payment_status_delete_error_with_message.tr(namedArgs: {'error': e.toString()});
      });
    }
  }

  /// Refresh the grid page after successful payment
  void _refreshGrid() {
    try {
      final bloc = GetIt.instance<RastalarApiBloc>();

      // Get the block ID from the current data
      String? blockId;
      if (widget.placeIds.isNotEmpty) {
        blockId = widget.placeIds.first; // Use the first place ID as block ID
      }

      bloc.add(RefreshSquares(
        blockId: blockId,
        pavilionId: widget.pavilionId,
      ));
      debugPrint(
          LocaleKeys.dialogs_payment_status_grid_refresh_success.tr(namedArgs: {'blockId': blockId.toString(), 'pavilionId': widget.pavilionId ?? 'null'}));
    } catch (e) {
      debugPrint(LocaleKeys.dialogs_payment_status_grid_refresh_error.tr(namedArgs: {'error': e.toString()}));
    }
  }

  /// Build cheque details widget
  Widget _buildChequeDetails() {
    if (_paymentData == null) return const SizedBox.shrink();

    final payment = _paymentData!;
    // ${LocaleKeys.dialogs_payment_status_date_format_comment.tr()}
    final dateStr = payment.date;
    String formattedDate = dateStr;
    String formattedTime = LocaleKeys.dialogs_payment_status_time_empty.tr();

    try {
      // Parse the date string to extract date and time
      if (dateStr.contains(LocaleKeys.dialogs_payment_status_date_contains_space.tr())) {
        final parts = dateStr.split(LocaleKeys.dialogs_payment_status_date_parts_separator.tr());
        final datePart = parts[0]; // ${LocaleKeys.dialogs_payment_status_date_part_index_0.tr()}
        final timePart = parts.length > 1 ? parts[1] : LocaleKeys.dialogs_payment_status_time_empty.tr(); // ${LocaleKeys.dialogs_payment_status_date_part_index_1.tr()}

        // ${LocaleKeys.dialogs_payment_status_date_format_conversion.tr()}
        final dateComponents = datePart.split(LocaleKeys.dialogs_payment_status_date_separator.tr());
        if (dateComponents.length == 3) {
          formattedDate =
              LocaleKeys.dialogs_payment_status_formatted_date_pattern.tr(namedArgs: {'day': dateComponents[2], 'month': dateComponents[1], 'year': dateComponents[0]});
        }

        formattedTime = timePart;
      }
    } catch (e) {
      // If parsing fails, use the original date string
      formattedDate = dateStr;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cCardsColor.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.cTextGrayColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.dialogs_payment_status_cheque_number.tr(),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.cTextGrayColor,
                ),
              ),
              Text(
                LocaleKeys.dialogs_payment_status_cheque_id_display.tr(namedArgs: {'id': payment.id.substring(payment.id.length - 8)}),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.dialogs_payment_status_created_at.tr(),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.cTextGrayColor,
                ),
              ),
              Text(
                LocaleKeys.dialogs_payment_status_formatted_datetime.tr(namedArgs: {'date': formattedDate, 'time': formattedTime}),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.dialogs_payment_status_days_count.tr(),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.cTextGrayColor,
                ),
              ),
              Text(
                LocaleKeys.dialogs_payment_status_days_display.tr(namedArgs: {'count': payment.day.toString()}),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.dialogs_payment_status_status.tr(),
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.cTextGrayColor,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _isPendingPayment
                      ? Colors.orange.withValues(alpha: 0.2)
                      : Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _isPendingPayment ? LocaleKeys.dialogs_payment_status_status_pending.tr() : LocaleKeys.dialogs_payment_status_status_new.tr(),
                  style: AppTextStyles.bodySmall.copyWith(
                    color: _isPendingPayment ? Colors.orange : Colors.green,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

enum PaymentStatus {
  creating,
  awaitingConfirmation,
  confirming,
  success,
  error,
}
