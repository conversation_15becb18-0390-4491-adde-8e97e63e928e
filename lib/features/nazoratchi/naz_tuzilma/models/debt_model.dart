import 'package:equatable/equatable.dart';

/// Model for debt response from API
class DebtResponse extends Equatable {
  final int debt;
  final String date;
  final int price;

  const DebtResponse({
    required this.debt,
    required this.date,
    required this.price,
  });

  /// Create from JSON response
  factory DebtResponse.fromJson(Map<String, dynamic> json) {
    return DebtResponse(
      debt: json['debt'] ?? 0,
      date: json['date'] ?? '',
      price: json['price'] ?? 0,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'debt': debt,
      'date': date,
      'price': price,
    };
  }

  /// Create empty instance
  factory DebtResponse.empty() {
    return const DebtResponse(
      debt: 0,
      date: '',
      price: 0,
    );
  }

  /// Get formatted debt amount with comma separators
  String get formattedDebt {
    if (debt == 0) return '0';
    return debt.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
    );
  }

  /// Get formatted price amount with comma separators
  String get formattedPrice {
    if (price == 0) return '0';
    return price.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
    );
  }

  /// Parse date string to DateTime (assumes format: "2025-07-24 15:04")
  DateTime? get parsedDate {
    try {
      return DateTime.parse(date.replaceFirst(' ', 'T'));
    } catch (e) {
      return null;
    }
  }

  /// Get formatted date string for display
  String get formattedDate {
    final parsed = parsedDate;
    if (parsed == null) return date;

    return '${parsed.day.toString().padLeft(2, '0')}/'
        '${parsed.month.toString().padLeft(2, '0')}/'
        '${parsed.year} '
        '${parsed.hour.toString().padLeft(2, '0')}:'
        '${parsed.minute.toString().padLeft(2, '0')}';
  }

  @override
  List<Object?> get props => [debt, date, price];
}