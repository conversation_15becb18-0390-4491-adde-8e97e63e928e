import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gap/gap.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../translations/locale_keys.g.dart';
import '../../../../core/widgets/universal_loading.dart';
import '../models/rastalar_model.dart';

/// Widget that displays the status legend for page grid
class RastalarStatusLegend extends StatelessWidget {
  final int? rastalarCount;
  final bool isLoading;

  const RastalarStatusLegend({
    super.key,
    required this.rastalarCount,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildShimmerSkeleton(context);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12),
          width: MediaQuery.of(context).size.width,
          height: 60,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: AppColors.cUnbindedColor.withAlpha(50),
            border: Border.all(color: AppColors.cUnbindedColor, width: 1),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.nazoratchi_market_structure_total_places.tr(),
                style: TextStyle(
                    color: AppColors.cTextGrayColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w400),
              ),
              Text(
                rastalarCount.toString(),
                style: TextStyle(
                    color: AppColors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.w500),
              )
            ],
          ),
        ),
        Gap(20),
        // Legend items in a 2x2 grid
        Row(
          children: [
            Expanded(
              child: _buildLegendItem(
                color: AppColors.cGreenishColor,
                label: LocaleKeys.nazoratchi_market_structure_legend_paid.tr(),
                status: SquareStatus.paid,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildLegendItem(
                color: AppColors.cReddishColor,
                label: LocaleKeys.nazoratchi_market_structure_legend_unpaid.tr(),
                status: SquareStatus.unpaid,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.w),
        Row(
          children: [
            Expanded(
              child: _buildLegendItem(
                color: AppColors.cYellowishColor,
                label: LocaleKeys.nazoratchi_market_structure_legend_empty.tr(),
                status: SquareStatus.available,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildLegendItem(
                color: AppColors.cUnbindedColor,
                label: LocaleKeys.nazoratchi_market_structure_legend_unassigned.tr(),
                status: SquareStatus.unbinded,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build shimmer skeleton that matches the original layout
  Widget _buildShimmerSkeleton(BuildContext context) {
    return UniversalLoading.shimmer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Total count container skeleton
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12),
            width: MediaQuery.of(context).size.width,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: AppColors.white.withValues(alpha: 0.2),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 80,
                  height: 14,
                  decoration: BoxDecoration(
                    color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                Container(
                  width: 40,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
          Gap(20),
          // Legend items skeleton in a 2x2 grid
          Row(
            children: [
              Expanded(child: _buildShimmerLegendItem()),
              SizedBox(width: 16.w),
              Expanded(child: _buildShimmerLegendItem()),
            ],
          ),
          SizedBox(height: 12.w),
          Row(
            children: [
              Expanded(child: _buildShimmerLegendItem()),
              SizedBox(width: 16.w),
              Expanded(child: _buildShimmerLegendItem()),
            ],
          ),
        ],
      ),
    );
  }

  /// Build shimmer legend item skeleton
  Widget _buildShimmerLegendItem() {
    return Row(
      children: [
        // Color indicator skeleton
        Container(
          width: 16.h,
          height: 16.h,
          decoration: BoxDecoration(
            color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 8),
        // Label skeleton
        Expanded(
          child: Container(
            height: 16,
            decoration: BoxDecoration(
              color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem({
    required Color color,
    required String label,
    required SquareStatus status,
  }) {
    return Row(
      children: [
        // Color indicator
        Container(
          width: 16.h,
          height: 16.h,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 8),
        // Label
        Expanded(
          child: Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w400,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
