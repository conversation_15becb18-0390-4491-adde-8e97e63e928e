import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../translations/locale_keys.g.dart';

class StatisticsLegend extends StatelessWidget {
  final int? successCount;
  final int? failureCount;
  final int? bothCount;
  final int? belgilanmaganCount;

  const StatisticsLegend({
    super.key,
    required this.successCount,
    required this.failureCount,
    required this.bothCount,
    required this.belgilanmaganCount,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _LegendItem(
          color: AppColors.cGreenishColor,
          title: LocaleKeys.nazoratchi_statistics_legend_paid_places.tr(),
          count: successCount,
        ),
        const SizedBox(height: 12),
        _LegendItem(
          color: AppColors.cReddishColor,
          title: LocaleKeys.nazoratchi_statistics_legend_unpaid_places.tr(),
          count: failureCount,
        ),
        const SizedBox(height: 12),
        _LegendItem(
          color: AppColors.cYellowishColor,
          title:LocaleKeys.nazoratchi_statistics_legend_empty_places.tr(),
          count: bothCount,
        ),
        const SizedBox(height: 12),
        _LegendItem(
          color: Color(0xFF313334),
          title:LocaleKeys.nazoratchi_statistics_legend_unassigned_places.tr() ,
          count: belgilanmaganCount,
        ),
      ],
    );
  }
}

class _LegendItem extends StatelessWidget {
  final Color color;
  final String title;
  final int? count;

  const _LegendItem({
    required this.color,
    required this.title,
    required this.count,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(2.r)
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.secondaryText,
            ),
          ),
        ),
        Text(
          count.toString(),
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
