import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../core/utils/app_functions.dart';

class StatisticsCard extends StatelessWidget {
  final String title;
  final String value;
  final Color? valueColor;
  final IconData? icon;
  final Color? iconColor;

  const StatisticsCard({
    super.key,
    required this.title,
    required this.value,
    this.valueColor,
    this.icon,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cCardsColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.cGrayBorderColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          if (icon != null) ...[
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: iconColor ?? AppColors.cGreenishColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
                const SizedBox(height: 4),
                Flexible(
                  child: Text(
                    value,
                    style: AppTextStyles.titleMedium.copyWith(
                      color: valueColor ?? AppColors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: AppFunctions.getResponsiveFontSize(value,
                              baseFontSize: 16.0)
                          .sp,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class StatisticsMetricCard extends StatelessWidget {
  final VoidCallback onTap;
  final String title;
  final String value;
  final Color backgroundColor;
  final String? icon;
  final bool expanded;
  final Color mainTextColor;
  final Color? iconColor;

  const StatisticsMetricCard({
    super.key,
    required this.title,
    required this.value,
    required this.backgroundColor,
    this.icon,
    this.expanded = true,
    required this.mainTextColor,
    this.iconColor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final cardWidget = IntrinsicHeight(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Material(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: InkWell(
            onTap: () {
              onTap();
            },
            child: Container(
              height: 100,
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Row(
                    children: [
                      if (icon != null) ...[
                        _buildIcon(icon!, iconColor ?? backgroundColor),
                        const SizedBox(width: 6),
                      ],
                      Flexible(
                        child: Text(
                          title,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.white,
                            fontSize: 12.sp,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    value,
                    style: AppTextStyles.titleSmall.copyWith(
                        color: mainTextColor,
                        fontWeight: FontWeight.w600,
                        fontSize: AppFunctions.getResponsiveFontSize(value,
                            baseFontSize: 20.0)),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    return expanded ? Expanded(child: cardWidget) : cardWidget;
  }

  Widget _buildIcon(String iconPath, Color color) {
    // Check if the icon path is an SVG file
    if (iconPath.toLowerCase().endsWith('.svg')) {
      return SvgPicture.asset(
        iconPath,
        width: 24,
        height: 24,
        colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
      );
    } else {
      // Handle other image formats (PNG, JPG, etc.)
      return ColorFiltered(
        colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
        child: Image.asset(
          iconPath,
          width: 24,
          height: 24,
          fit: BoxFit.contain,
        ),
      );
    }
  }
}
