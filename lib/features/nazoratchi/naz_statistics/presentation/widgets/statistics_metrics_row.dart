import 'package:click_bazaar/generated/assets.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/utils/app_functions.dart';
import '../pages/statistics_history_page.dart';
import 'statistics_card.dart';

class StatisticsMetricsRow extends StatelessWidget {
  final int? rejda;
  final int? tushgan;
  final int? clickOrqali;
  final int? naqdPul;
  final int? terminalOrqali;
  final int? qarzdalik;
  final String date;

  const StatisticsMetricsRow({
    super.key,
    required this.rejda,
    required this.tushgan,
    required this.clickOrqali,
    required this.naqdPul,
    required this.terminalOrqali,
    required this.qarzdalik,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // First row - <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>
        IntrinsicHeight(
          child: Row(
            children: [
              StatisticsMetricCard(
                title: LocaleKeys.nazoratchi_statistics_plan_in_plan.tr(),
                value: AppFunctions.formatNumberSafe(rejda),
                backgroundColor: AppColors.cFirstColor.withValues(alpha: 0.1),
                mainTextColor: AppColors.cFirstColor,
                icon: Assets.iconsRejada,
                iconColor: AppColors.cFirstColor,
                onTap: () {},
              ),
              const SizedBox(width: 12),
              StatisticsMetricCard(
                title: LocaleKeys.nazoratchi_statistics_plan_received.tr(),
                value: AppFunctions.formatNumberSafe(tushgan),
                backgroundColor:
                    AppColors.cGreenishColor.withValues(alpha: 0.1),
                mainTextColor: AppColors.cGreenishColor,
                icon: Assets.iconsTushgan,
                iconColor: AppColors.cGreenishColor,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => StatisticsHistoryPage(
                                title: LocaleKeys
                                    .nazoratchi_statistics_plan_received
                                    .tr(),
                                date: date,
                                paymentType: null,
                                payment: null,
                              )));
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        IntrinsicHeight(
          child: Row(
            children: [
              StatisticsMetricCard(
                title: LocaleKeys.nazoratchi_statistics_plan_via_click.tr(),
                value: AppFunctions.formatNumberSafe(clickOrqali),
                backgroundColor: AppColors.cCardsColor,
                icon: Assets.iconsClick,
                mainTextColor: AppColors.white,
                iconColor: AppColors.cFirstColor,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => StatisticsHistoryPage(
                                title: LocaleKeys
                                    .nazoratchi_statistics_plan_via_click
                                    .tr(),
                                date: date,
                                paymentType: 3,
                                payment: null,
                              )));
                },
              ),
              const SizedBox(width: 12),
              StatisticsMetricCard(
                title: LocaleKeys.nazoratchi_statistics_plan_cash.tr(),
                value: AppFunctions.formatNumberSafe(naqdPul),
                backgroundColor: AppColors.cCardsColor,
                icon: Assets.iconsWallet,
                mainTextColor: AppColors.white,
                iconColor: AppColors.cGreenishColor,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => StatisticsHistoryPage(
                                title: LocaleKeys
                                    .nazoratchi_statistics_plan_cash
                                    .tr(),
                                date: date,
                                paymentType: 1,
                                payment: null,
                              )));
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        IntrinsicHeight(
          child: Row(
            children: [
              StatisticsMetricCard(
                title: LocaleKeys.nazoratchi_statistics_plan_via_terminal.tr(),
                value: AppFunctions.formatNumberSafe(terminalOrqali),
                backgroundColor:
                    AppColors.cYellowishColor.withValues(alpha: 0.2),
                mainTextColor: AppColors.white,
                icon: Assets.iconsCardPos,
                iconColor: AppColors.cYellowishColor,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => StatisticsHistoryPage(
                                title: LocaleKeys
                                    .nazoratchi_statistics_plan_via_terminal
                                    .tr(),
                                date: date,
                                paymentType: 2,
                                payment: null,
                              )));
                },
              ),
              const SizedBox(width: 12),
              StatisticsMetricCard(
                title: LocaleKeys.nazoratchi_statistics_plan_debt.tr(),
                value: AppFunctions.formatNumberSafe(qarzdalik),
                backgroundColor: AppColors.cReddishColor.withValues(alpha: 0.1),
                mainTextColor: AppColors.cReddishColor,
                icon: Assets.iconsQarzdorlik,
                iconColor: AppColors.cReddishColor,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => StatisticsHistoryPage(
                                title: LocaleKeys
                                    .nazoratchi_statistics_plan_debt
                                    .tr(),
                                date: date,
                                paymentType: null,
                                payment: false,
                              )));
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
