import 'dart:async';
import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:click_bazaar/core/theme/app_text_styles.dart';
import 'package:click_bazaar/core/utils/app_functions.dart';
import 'package:click_bazaar/core/widgets/universal_loading.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/models/payment_history_model.dart';
import 'package:click_bazaar/di/dependency_injection.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../bloc/statistics_history_bloc/payment_history_bloc.dart';

/// Page displaying payment history with pagination
class StatisticsHistoryPage extends StatelessWidget {
  final String title;
  final String date;
  final int? paymentType;
  final bool? payment;

  const StatisticsHistoryPage({
    super.key,
    required this.title,
    required this.date,
    required this.paymentType,
    required this.payment,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di<NazPaymentHistoryBloc>()
        ..add(LoadPaymentHistoryEvent(
          paymentType: paymentType,
          payment: payment,
          date: date,
          limit: 50, // Increased limit to ensure we get all data
        )),
      child: _StatisticsHistoryView(title: title),
    );
  }
}

class _StatisticsHistoryView extends StatefulWidget {
  final String title;

  const _StatisticsHistoryView({required this.title});

  @override
  State<_StatisticsHistoryView> createState() => _StatisticsHistoryViewState();
}

class _StatisticsHistoryViewState extends State<_StatisticsHistoryView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final bloc = context.read<NazPaymentHistoryBloc>();
    final state = bloc.state;

    // Only load more if we're near the bottom, not already loading, and there's more data
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 100 &&
        !state.isLoadingMore &&
        state.hasNextPage) {
      print('Loading more data... Current items: ${state.currentItemsCount}, hasNextPage: ${state.hasNextPage}');
      bloc.add(const LoadMorePaymentHistoryEvent());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cBackgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.cBackgroundColor,
        elevation: 0,
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: AppColors.white,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          widget.title,
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: BlocConsumer<NazPaymentHistoryBloc, PaymentHistoryState>(
        listener: (context, state) {
          if (state.hasRefreshError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message ?? LocaleKeys.statistics_history_error_occurred.tr()),
                backgroundColor: AppColors.cReddishColor,
              ),
            );
          }
          if (state.hasLoadMoreError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message ?? LocaleKeys.statistics_history_load_more_error.tr()),
                backgroundColor: AppColors.cReddishColor,
              ),
            );
          }
        },
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: () async {
              final completer = Completer<void>();
              context.read<NazPaymentHistoryBloc>().add(const RefreshPaymentHistoryEvent());

              // Wait for refresh to complete
              Timer.periodic(const Duration(milliseconds: 100), (timer) {
                final currentState = context.read<NazPaymentHistoryBloc>().state;
                if (!currentState.isRefreshing) {
                  timer.cancel();
                  completer.complete();
                }
              });

              return completer.future;
            },
            child: _buildBody(state),
          );
        },
      ),
    );
  }

  Widget _buildBody(PaymentHistoryState state) {
    if (state.isLoading) {
      return _buildLoadingState();
    }

    if (state.isFailure && !state.hasData) {
      return _buildErrorState(state);
    }

    // Show shimmer during refresh if we have existing data
    if (state.isRefreshing && state.hasData) {
      return _buildRefreshLoadingState();
    }

    return _buildSuccessState(state);
  }

  Widget _buildLoadingState() {
    return UniversalLoading.shimmer(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            // Header shimmer
            _buildHeaderShimmer(),
            SizedBox(height: 16.h),
            // List shimmer
            Expanded(
              child: ListView.builder(
                itemCount: 10,
                itemBuilder: (context, index) => _buildListItemShimmer(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRefreshLoadingState() {
    return UniversalLoading.shimmer(
      child: Padding(
        padding: EdgeInsets.all(8),
        child: Column(
          children: [
            // Header
            _buildHeader(),
            SizedBox(height: 16),
            // Shimmer list
            Expanded(
              child: ListView.builder(
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: 8,
                itemBuilder: (context, index) => _buildListItemShimmer(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderShimmer() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 16.h,
              color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Container(
              height: 16.h,
              color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItemShimmer() {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 16.h,
            color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
          ),
          const Spacer(),
          Container(
            width: 80.w,
            height: 16.h,
            color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(PaymentHistoryState state) {
    return Center(
      child: UniversalLoading.error(
        message: state.message ?? LocaleKeys.statistics_history_data_load_error.tr(),
        onRetry: () {
          context.read<NazPaymentHistoryBloc>().add(LoadPaymentHistoryEvent(
            paymentType: state.currentFilters?.paymentType,
            payment: state.currentFilters?.payment,
            date: state.currentFilters?.date,
            limit: state.currentFilters?.limit ?? 10,
          ));
        },
        retryButtonText: LocaleKeys.statistics_history_retry.tr(),
      ),
    );
  }

  Widget _buildSuccessState(PaymentHistoryState state) {
    final paymentHistory = state.paymentHistory;
    if (paymentHistory == null || paymentHistory.docs.isEmpty) {
      return CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverFillRemaining(
            child: Center(
              child: Text(
                LocaleKeys.statistics_history_no_data_found.tr(),
                style: TextStyle(color: AppColors.white),
              ),
            ),
          ),
        ],
      );
    }

    return CustomScrollView(
      controller: _scrollController,
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: EdgeInsets.all(8),
            child: _buildHeader(),
          ),
        ),
        SliverToBoxAdapter(
          child: SizedBox(height: 16),
        ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              if (index == paymentHistory.docs.length) {
                // Loading more indicator
                return const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: CircularProgressIndicator(
                      color: AppColors.cFirstColor,
                    ),
                  ),
                );
              }
              final item = paymentHistory.docs[index];
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: _buildPaymentHistoryItem(item),
              );
            },
            childCount: paymentHistory.docs.length + (state.isLoadingMore ? 1 : 0),
          ),
        ),
        // Add extra space to ensure scrollability
        SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      decoration: BoxDecoration(),
      child: Row(
        children: [
          Text(
            LocaleKeys.statistics_history_places_header.tr(),
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.cTextGrayColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              LocaleKeys.statistics_history_amount_header.tr(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.cTextGrayColor,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentHistoryItem(PaymentHistoryItem item) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Text(
            '#${item.title}',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          Text(
            AppFunctions.formatNumber(item.price),
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

}
