import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../translations/locale_keys.g.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cBackgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.cFirstColor,
        title: Text(LocaleKeys.face_control_about_page_title.tr()),
        toolbarHeight: 70,
        centerTitle: true,
      ),
      body: Container(
          margin: const EdgeInsets.only(left: 16.0, right: 16.0),
          child: Column(children: [
            const SizedBox(height: 10),
            Text(
                LocaleKeys.face_control_about_page_developer_message.tr(), textAlign: TextAlign.center,),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(
                  Icons.email,
                  color: Colors.black,
                  weight: 24,
                ),
                const SizedBox(width: 4),
                Text(LocaleKeys.face_control_about_page_email.tr())
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(
                  Icons.phone,
                  color: Colors.black,
                  weight: 24,
                ),
                const SizedBox(width: 4),
                Text(LocaleKeys.face_control_about_page_phone.tr())
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(
                  Icons.telegram,
                  color: Colors.black,
                  weight: 24,
                ),
                const SizedBox(width: 4),
                Text(LocaleKeys.face_control_about_page_telegram.tr())
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(
                  Icons.code,
                  color: Colors.black,
                  weight: 24,
                ),
                const SizedBox(width: 4),
                Text(LocaleKeys.face_control_about_page_github.tr())
              ],
            ),
          ])),
    );
  }
}
