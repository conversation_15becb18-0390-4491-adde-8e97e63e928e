import 'dart:io';
import 'package:camera/camera.dart';
import 'package:click_bazaar/features/camera/image_picker_utils.dart';
import 'package:flutter/foundation.dart';

import 'package:click_bazaar/core/network/interceptors.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/services/logout_service.dart';
import 'package:click_bazaar/core/services/navigation_service.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/features/auth/datasources/login_remote_datasources.dart';
import 'package:click_bazaar/features/auth/datasources/sms_verification_remote_datasource.dart';
import 'package:click_bazaar/features/auth/presentation/bloc/login_bloc/login_bloc.dart';
import 'package:click_bazaar/features/auth/presentation/bloc/sms_verification_bloc/sms_verification_bloc.dart';
import 'package:click_bazaar/features/nazoratchi/naz_profile/datasources/naz_profile_remote_datasource.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/datasources/naz_rastalar_remote_datasource.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/bloc/rastalar_api/rastalar_api_bloc.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/datasources/naz_boz_tuzilma_remote_datasource.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/datasources/naz_pavilion_remote_datasource.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/bloc/naz_boz_tuzilma/naz_boz_tuzilma_bloc.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/bloc/naz_pavilion/naz_pavilion_bloc.dart';
import 'package:click_bazaar/features/nazoratchi/naz_profile/presentation/bloc/naz_profile_bloc.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/datasources/naz_statistics_remote_datasource.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/datasources/payment_history_remote_datasource.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/presentation/bloc/naz_statistics_bloc/naz_statistics_bloc.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/services/payment_service.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/services/debt_service.dart';
import 'package:click_bazaar/features/sotuvchi/sot_profile/data/datasources/sot_profile_remote_datasource.dart';
import 'package:click_bazaar/features/sotuvchi/sot_profile/presentation/blocs/sot_profile_bloc.dart';
import 'package:click_bazaar/features/sotuvchi/sot_empty_places/datasources/sot_empty_places_remote_datasource.dart';
import 'package:click_bazaar/features/sotuvchi/sot_empty_places/presentation/bloc/sot_empty_places_bloc.dart';
import 'package:click_bazaar/features/sotuvchi/sot_payment_history/datasources/payment_history_remote_datasource.dart';
import 'package:click_bazaar/features/sotuvchi/sot_payment_history/presentation/bloc/payment_history_bloc.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:get_storage/get_storage.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../features/nazoratchi/naz_statistics/data/datasources/payment_history_remote_datasource.dart';
import '../features/nazoratchi/naz_statistics/presentation/bloc/statistics_history_bloc/payment_history_bloc.dart';

final di = GetIt.instance;

Future<void> init() async {
  await GetStorage.init();
  di.registerLazySingleton(() => GetStorage());
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  di.registerLazySingleton(() => prefs);
  di.registerLazySingleton(() => FlutterSecureStorage());

  // Register LogoutService
  di.registerLazySingleton<LogoutService>(() => LogoutService(storage: di()));

  // Register NavigationService
  di.registerLazySingleton<NavigationService>(() => NavigationService());

  // Register NetworkInfo
  di.registerLazySingleton<InternetConnectionChecker>(
      () => InternetConnectionChecker.createInstance(
            addresses: [
              AddressCheckOption(
                uri: Uri.parse('https://www.google.com'),
                timeout: const Duration(seconds: 10),
              ),
            ],
          ));
  di.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(di()));

  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  APP_VERSION = packageInfo.version;

  // Only register device info on mobile platforms
  if (!kIsWeb && Platform.isAndroid) {
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    final androidInfo = await deviceInfoPlugin.androidInfo;
    di.registerLazySingleton(() => androidInfo);
  }
  //Dio interceptor
  final Dio dio = Dio(BaseOptions(
      // baseUrl: baseUrl,
      connectTimeout: Duration(seconds: 20),
      receiveTimeout: Duration(seconds: 20),
      sendTimeout: Duration(seconds: 20)));
  di.registerLazySingleton<Dio>(() => dio);
  //dio.interceptors.add(alice.getDioInterceptor());
  dio.interceptors
      .add(AppInterceptor(storage: di(), networkInfo: di(), dio: di()));

  // Register Auth dependencies
  di.registerLazySingleton<LoginRemoteDatasourceImpl>(
    () => LoginRemoteDatasourceImpl(
      networkInfo: di(),
      dio: di(),
    ),
  );

  ///Camera
  // Obtain a list of the available cameras on the device.
  var cameras = await availableCameras();

  di.registerLazySingleton<List<CameraDescription>>(() => cameras);

  // Image picker
  di.registerLazySingleton<ImagePickerUtils>(() => ImagePickerUtilsImpl());

  di.registerLazySingleton<SmsVerificationRemoteDatasourceImpl>(
    () => SmsVerificationRemoteDatasourceImpl(
      networkInfo: di(),
      dio: di(),
    ),
  );

  di.registerFactory<LoginBloc>(
    () => LoginBloc(
      networkInfo: di(),
      loginRemoteDatasource: di(),
    ),
  );

  di.registerFactory<SmsVerificationBloc>(
    () => SmsVerificationBloc(
      networkInfo: di(),
      smsVerificationRemoteDatasource: di(),
      storage: di(),
    ),
  );

  // Register Naz Profile dependencies
  di.registerLazySingleton<NazProfileRemoteDatasource>(
    () => NazProfileRemoteDatasourceImpl(
      dio: di(),
      networkInfo: di(),
    ),
  );

  di.registerFactory<NazProfileBloc>(
    () => NazProfileBloc(
      remoteDatasource: di(),
      storage: di(),
      networkInfo: di(),
    ),
  );

  // Register Sot Profile dependencies
  di.registerLazySingleton<SotProfileRemoteDatasource>(
    () => SotProfileRemoteDatasourceImpl(
      dio: di(),
      networkInfo: di(),
    ),
  );

  di.registerFactory<SotProfileBloc>(
    () => SotProfileBloc(
      remoteDatasource: di(),
      storage: di(),
      networkInfo: di(),
    ),
  );

  // Register Sot Empty Places dependencies
  di.registerLazySingleton<SotEmptyPlacesRemoteDatasource>(
    () => SotEmptyPlacesRemoteDatasourceImpl(
      dio: di(),
      networkInfo: di(),
    ),
  );

  di.registerFactory<SotEmptyPlacesBloc>(
    () => SotEmptyPlacesBloc(
      remoteDatasource: di(),
      networkInfo: di(),
    ),
  );

  // Register Sot Payment History dependencies
  di.registerLazySingleton<PaymentHistoryRemoteDatasource>(
    () => PaymentHistoryRemoteDatasourceImpl(
      dio: di(),
      networkInfo: di(),
    ),
  );

  di.registerFactory<PaymentHistoryBloc>(
    () => PaymentHistoryBloc(
      remoteDatasource: di(),
      networkInfo: di(),
    ),
  );

  // Register Naz Boz Tuzilma dependencies
  di.registerLazySingleton<NazBozTuzilmaRemoteDatasource>(
    () => NazBozTuzilmaRemoteDatasourceImpl(
      dio: di(),
      networkInfo: di(),
      storage: di(),
    ),
  );


  di.registerFactory<NazBozTuzilmaBloc>(
    () => NazBozTuzilmaBloc(
      remoteDatasource: di(),
      networkInfo: di(),
    ),
  );

  // Register Naz Pavilion dependencies
  di.registerLazySingleton<NazPavilionRemoteDatasource>(
    () => NazPavilionRemoteDatasourceImpl(
      dio: di(),
      networkInfo: di(),
    ),
  );

  di.registerFactory<NazPavilionBloc>(
    () => NazPavilionBloc(
      remoteDatasource: di(),
      networkInfo: di(),
    ),
  );

  // Rastalar Remote Datasource
  di.registerLazySingleton<NazRastalarRemoteDatasourceImpl>(
    () => NazRastalarRemoteDatasourceImpl(dio: di()),
  );

  // Rastalar API BLoC
  di.registerFactory<RastalarApiBloc>(
    () => RastalarApiBloc(
      networkInfo: di(),
      remoteDatasource: di(),
    ),
  );

  // Register Naz Statistics dependencies
  di.registerLazySingleton<NazStatisticsRemoteDatasourceImpl>(
    () => NazStatisticsRemoteDatasourceImpl(
      networkInfo: di(),
      dio: di(),
    ),
  );

  di.registerFactory<NazStatisticsBloc>(
    () => NazStatisticsBloc(
      networkInfo: di(),
      nazStatisticsRemoteDatasource: di(),
    ),
  );

  // Register Naz Payment History dependencies
  di.registerLazySingleton<NazPaymentHistoryRemoteDatasourceImpl>(
        () => NazPaymentHistoryRemoteDatasourceImpl(
      networkInfo: di(),
      dio: di(),
    ),
  );

  di.registerFactory<NazPaymentHistoryBloc>(
        () => NazPaymentHistoryBloc(
      networkInfo: di(),
      paymentHistoryRemoteDatasource: di(),
    ),
  );

  ///Update
  // Check for in app updates

  AppUpdateInfo updateInfo = AppUpdateInfo(
      updateAvailability: UpdateAvailability.unknown,
      immediateUpdateAllowed: false,
      immediateAllowedPreconditions: [],
      flexibleUpdateAllowed: false,
      flexibleAllowedPreconditions: [],
      availableVersionCode: 0,
      installStatus: InstallStatus.unknown,
      packageName: 'com.example.uz',
      clientVersionStalenessDays: 0,
      updatePriority: 0);

  if (!kIsWeb && Platform.isAndroid) {
    try {
      updateInfo = await InAppUpdate.checkForUpdate();
    } catch (e) {
      print("Error in 'In-AppUpdate:' $e");
    }
  }
  di.registerLazySingleton<AppUpdateInfo>(() => updateInfo);

  // Register Payment Service
  di.registerLazySingleton<PaymentService>(() => PaymentService(dio: di()));

  // Register Debt Service
  di.registerLazySingleton<DebtService>(() => DebtService(dio: di()));
}
