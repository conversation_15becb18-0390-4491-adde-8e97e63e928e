import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:click_bazaar/core/extensions/context_extensions.dart';
import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:click_bazaar/core/widgets/custom_toast.dart';
import 'package:click_bazaar/core/widgets/custom_app_dialog.dart';
import 'package:click_bazaar/features/face_control/definitions.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/models/pavilion_model.dart';
import 'package:click_bazaar/features/auth/models/user_profile_model.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http_parser/http_parser.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../di/dependency_injection.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../translations/codegen_loader.g.dart';
import '../../translations/locale_keys.g.dart';
import '../services/logout_service.dart';
import '../theme/app_text_styles.dart';

/// Show custom permission dialog using app's design system
showCustomDialog(BuildContext context, [bool? isDismissible]) {
  WidgetsBinding.instance.addPostFrameCallback((time) {
    showDialog(
      context: context,
      barrierDismissible: isDismissible ?? true,
      builder: (context) => PopScope(
        canPop: isDismissible ?? true,
        child: CustomAppDialog(
          isCloseVisible: isDismissible ?? true,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Permission icon
              Container(
                width: 80.w,
                height: 80.h,
                decoration: BoxDecoration(
                  color: AppColors.cFirstColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.security,
                  color: AppColors.cFirstColor,
                  size: 40.w,
                ),
              ),

              SizedBox(height: 24.h),

              // Title
              Text(
                "Ruxsatlar",
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 16.h),

              // Message text
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: context.isTablet ? 10.w : 0.w,
                ),
                child: Text(
                  "Kamera va mikrofon ruxsatlarini bering",
                  style: TextStyle(
                    color: AppColors.cTextGrayColor,
                    fontSize: context.isTablet ? 16.sp : 18.sp,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              SizedBox(height: 32.h),

              // Action button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () async {
                    var switcher = isDismissible ?? true;
                    if (!switcher) {
                      Navigator.pop(context);
                      // await PackageUsageStats.openAppUsageSettings();
                    } else {
                      Navigator.pop(context);
                      await openAppSettings();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.cFirstColor,
                    foregroundColor: AppColors.white,
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: Text(
                    "Davom etish",
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  });
}

MediaType getMediaType(String path) {
  final extension = path.split('.').last.toLowerCase();

  switch (extension) {
    case 'pdf':
      return MediaType('application', 'pdf');
    case 'jpg':
    case 'jpeg':
      return MediaType('image', 'jpeg');
    case 'png':
      return MediaType('image', 'png');
    case 'doc':
    case 'docx':
      return MediaType('application', 'msword');
    case 'xls':
    case 'xlsx':
      return MediaType('application', 'vnd.ms-excel');
    default:
      return MediaType('application', 'octet-stream');
  }
}

int rastalarCount(BlockData blockData) {
  return blockData.availableStatuses
      .fold(0, (sum, status) => sum + status.count);
}

Future<void> deleteFileFromInternalStorage(String fileName,
    {bool withPath = true}) async {
  try {
    // Get the application documents directory
    Directory appDocumentsDirectory = await getApplicationDocumentsDirectory();

    // Create a file path
    String filePath;
    if (!withPath) {
      filePath = '${appDocumentsDirectory.path}/$fileName';
    } else {
      filePath = fileName;
    }

    // Check if the file exists before attempting to delete
    if (await File(filePath).exists()) {
      // Delete the file
      await File(filePath).delete(recursive: true);
      print('File deleted successfully: $filePath');
    } else {
      print('File not found: $filePath');
      // CustomToast.showToast('File not found: $filePath');
    }
  } catch (e) {
    print('Error deleting file: $e');
    CustomToast.showToast('Error deleting file: $e');
  }
}

/// Save file to internal storage and return the saved path
Future<String> saveFileToInternalStorage(String sourcePath) async {
  try {
    // Get the application documents directory
    Directory appDocumentsDirectory = await getApplicationDocumentsDirectory();

    // Extract filename from source path
    String fileName = sourcePath.split('/').last;

    // Create destination path
    String destinationPath = '${appDocumentsDirectory.path}/$fileName';

    // Copy file to internal storage
    File sourceFile = File(sourcePath);
    if (await sourceFile.exists()) {
      File destinationFile = await sourceFile.copy(destinationPath);
      print('File saved to internal storage: $destinationPath');
      return destinationFile.path;
    } else {
      print('Source file not found: $sourcePath');
      return sourcePath; // Return original path if source doesn't exist
    }
  } catch (e) {
    print('Error saving file to internal storage: $e');
    CustomToast.showToast('Error saving file: $e');
    return sourcePath; // Return original path on error
  }
}

/// Create notification function placeholder
/// This is a placeholder function for creating notifications
createNotification(String title) => AwesomeNotifications().createNotification(
    content: NotificationContent(
        id: 0,
        backgroundColor: AppColors.cFirstColor,
        color: AppColors.cFirstColor,
        channelKey: CHANNEL_KEY,
        title: title));

// ============================================================================
// UNIVERSAL CACHING FUNCTIONS
// ============================================================================

/// Universal cache configuration
class CacheConfig {
  static const Duration defaultCacheValidDuration = Duration(minutes: 30);
  static const String cacheTimeKeySuffix = '_cache_time';
}

/// Universal cache functions for any data model
class UniversalCache {
  final GetStorage _storage;
  final Duration _cacheValidDuration;

  UniversalCache({
    GetStorage? storage,
    Duration? cacheValidDuration,
  })  : _storage = storage ?? GetStorage(),
        _cacheValidDuration =
            cacheValidDuration ?? CacheConfig.defaultCacheValidDuration;

  /// Cache any data model with toJson() method
  Future<void> cacheData<T>({
    required String cacheKey,
    required T data,
    required Map<String, dynamic> Function() toJsonFunction,
  }) async {
    try {
      await _storage.write(cacheKey, toJsonFunction());
      await _storage.write('${cacheKey}${CacheConfig.cacheTimeKeySuffix}',
          DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      print('Error caching data for key $cacheKey: $e');
      // Ignore cache errors - don't break the app
    }
  }

  /// Get cached data and convert back to model
  Future<T?> getCachedData<T>({
    required String cacheKey,
    required T Function(Map<String, dynamic>) fromJsonFunction,
  }) async {
    try {
      final cachedData = _storage.read(cacheKey);
      if (cachedData != null) {
        return fromJsonFunction(Map<String, dynamic>.from(cachedData));
      }
    } catch (e) {
      print('Error getting cached data for key $cacheKey: $e');
      // Ignore cache errors
    }
    return null;
  }

  /// Clear cached data
  Future<void> clearCachedData(String cacheKey) async {
    try {
      await _storage.remove(cacheKey);
      await _storage.remove('${cacheKey}${CacheConfig.cacheTimeKeySuffix}');
    } catch (e) {
      print('Error clearing cached data for key $cacheKey: $e');
      // Ignore cache errors
    }
  }

  /// Check if cache is valid for a specific key
  bool isCacheValid(String cacheKey) {
    try {
      final cacheTime =
          _storage.read('${cacheKey}${CacheConfig.cacheTimeKeySuffix}');
      if (cacheTime != null) {
        final cachedDateTime = DateTime.fromMillisecondsSinceEpoch(cacheTime);
        final now = DateTime.now();
        return now.difference(cachedDateTime) < _cacheValidDuration;
      }
    } catch (e) {
      print('Error checking cache validity for key $cacheKey: $e');
      // Ignore cache errors
    }
    return false;
  }

  /// Get cached data only if cache is valid
  Future<T?> getCachedDataIfValid<T>({
    required String cacheKey,
    required T Function(Map<String, dynamic>) fromJsonFunction,
  }) async {
    if (isCacheValid(cacheKey)) {
      return await getCachedData<T>(
        cacheKey: cacheKey,
        fromJsonFunction: fromJsonFunction,
      );
    }
    return null;
  }

  /// Cache data with custom expiration time
  Future<void> cacheDataWithExpiration<T>({
    required String cacheKey,
    required T data,
    required Map<String, dynamic> Function() toJsonFunction,
    required Duration customExpiration,
  }) async {
    try {
      await _storage.write(cacheKey, toJsonFunction());
      final expirationTime =
          DateTime.now().add(customExpiration).millisecondsSinceEpoch;
      await _storage.write(
          '${cacheKey}${CacheConfig.cacheTimeKeySuffix}', expirationTime);
    } catch (e) {
      print('Error caching data with expiration for key $cacheKey: $e');
      // Ignore cache errors
    }
  }
}

// ============================================================================
// CONVENIENT STATIC HELPER FUNCTIONS
// ============================================================================

/// Static helper functions for common caching operations
class CacheHelper {
  static final UniversalCache _defaultCache = UniversalCache();

  /// Cache user profile (works with any profile type)
  static Future<void> cacheUserProfile<T>({
    required String cacheKey,
    required T profile,
    required Map<String, dynamic> Function() toJsonFunction,
  }) async {
    await _defaultCache.cacheData<T>(
      cacheKey: cacheKey,
      data: profile,
      toJsonFunction: toJsonFunction,
    );
  }

  /// Get cached user profile
  static Future<T?> getCachedUserProfile<T>({
    required String cacheKey,
    required T Function(Map<String, dynamic>) fromJsonFunction,
  }) async {
    return await _defaultCache.getCachedDataIfValid<T>(
      cacheKey: cacheKey,
      fromJsonFunction: fromJsonFunction,
    );
  }

  /// Clear cached user profile
  static Future<void> clearCachedUserProfile(String cacheKey) async {
    await _defaultCache.clearCachedData(cacheKey);
  }

  /// Check if user profile cache is valid
  static bool isUserProfileCacheValid(String cacheKey) {
    return _defaultCache.isCacheValid(cacheKey);
  }
}

void showLogoutDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      backgroundColor: AppColors.cCardsColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      title: Text(
        LocaleKeys.profile_logout.tr(),
        style: AppTextStyles.titleMedium.copyWith(
          color: AppColors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      content: Text(
        LocaleKeys.profile_logout_confirmation.tr(),
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.cTextGrayColor,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            LocaleKeys.profile_cancel.tr(),
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.cTextGrayColor,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.pop(context);
            await logout(context);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.cReddishColor,
            foregroundColor: AppColors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          child: Text(
            LocaleKeys.profile_logout.tr(),
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    ),
  );
}

Future<void> logout(BuildContext context) async {
  try {
    // Use LogoutService for complete logout with storage erasure
    final logoutService = di<LogoutService>();
    await logoutService.performCompleteLogout(
      showMessage: false, // We'll show our own message
      context: context,
    );

    if (context.mounted) {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false,
      );
    }
  } catch (e) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${LocaleKeys.profile_logout_error.tr()}: $e'),
          backgroundColor: AppColors.cReddishColor,
        ),
      );
    }
  }
}

// ============================================================================
// PREDEFINED CACHE KEYS
// ============================================================================

/// Predefined cache keys for common use cases
class CacheKeys {
  // User profile cache keys
  static const String supervisorProfile = 'supervisor_profile';
  static const String sellerProfile = 'seller_profile';
  static const String sotuvchiProfile = 'sotuvchi_profile';
  static const String nazoratchiProfile = 'nazoratchi_profile';

  // Statistics cache keys
  static const String planStatistics = 'plan_statistics';
  static const String placeStatistics = 'place_statistics';
  static const String paymentStatistics = 'payment_statistics';

  // Other data cache keys
  static const String pavilionData = 'pavilion_data';
  static const String blockData = 'block_data';
  static const String freePlaces = 'free_places';
  static const String paymentHistory = 'payment_history';
}
