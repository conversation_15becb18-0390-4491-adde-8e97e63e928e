import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../translations/locale_keys.g.dart';


class CustomToast {
  static void showToast(String text) {
    Fluttertoast.showToast(
      msg: text,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.TOP,
      timeInSecForIosWeb: 1,
      backgroundColor: Colors.black38,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  /// Show error toast with red background
  static void showErrorToast(String text) {
    Fluttertoast.showToast(
      msg: text,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.TOP,
      timeInSecForIosWeb: 1,
      backgroundColor: AppColors.cReddishColor,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }
}

Snack(String msg, BuildContext ctx, Color color) {
  var snackBar = SnackBar(
      backgroundColor: color,
      content: Text(
        msg,
        textAlign: TextAlign.center,
      ));
  ScaffoldMessenger.of(ctx).showSnackBar(snackBar);
}

SnackAction(String msg, BuildContext ctx, Color color, Function fnc) {
  var snackBar = SnackBar(
      duration: Duration(seconds: 5),
      action: SnackBarAction(
        label: LocaleKeys.common_clear.tr(),
        onPressed: () {
          fnc();
        },
      ),
      backgroundColor: color,
      content: Text(
        msg,
        style: TextStyle(
            color: AppColors.cFirstColor,
            fontSize: 15.sp,
            fontWeight: FontWeight.bold),
        textAlign: TextAlign.center,
      ));
  ScaffoldMessenger.of(ctx).showSnackBar(snackBar);
}
