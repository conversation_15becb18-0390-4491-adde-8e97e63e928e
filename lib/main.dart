import 'dart:io';
import 'package:click_bazaar/features/face_control/lock_switcher.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_storage/get_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:intl/intl.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'core/location/location_service.dart';
import 'core/theme/app_theme.dart';
import 'core/theme/theme_manager.dart';
import 'core/theme/app_colors.dart';
import 'core/app/main_app_wrapper.dart';
import 'core/utils/app_constants.dart';
import 'core/utils/jwt_decoder.dart';
import 'core/widgets/role_switcher.dart';
import 'core/services/logout_service.dart';
import 'core/services/navigation_service.dart';
import 'features/auth/presentation/pages/login_page.dart';
import 'features/auth/presentation/pages/sms_verification_page.dart';
import 'features/auth/services/auth_service.dart';
import 'features/nazoratchi/naz_profile/presentation/bloc/naz_profile_bloc.dart';
import 'features/nazoratchi/naz_profile/presentation/bloc/naz_profile_event.dart';
import 'features/splash/splash_screen.dart';
import 'features/face_control/definitions.dart';
import 'features/face_control/functional_lock_page.dart';
import 'core/function/functions.dart';
import 'main_naz_navigation_page.dart';
import 'main_sot_navigation_page.dart';
import 'di/dependency_injection.dart' as di;
import 'firebase_options.dart';
import 'core/services/language_service.dart';
import 'env.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  // Initialize environment
  AppEnvironment.setupEnv(Environment.prod);
  await di.init();

  // Get saved language or default to Uzbek
  final savedLanguage = LanguageService.getCurrentLanguage();

  runApp(
    EasyLocalization(
      supportedLocales: const [
        Locale('uz'),
        Locale('ru'),
      ],
      path: 'assets/translations',
      fallbackLocale: const Locale('uz'),
      startLocale: Locale(savedLanguage),
      child: const ClickBazaarApp(),
    ),
  );
}

class ClickBazaarApp extends StatefulWidget {
  const ClickBazaarApp({super.key});

  @override
  State<ClickBazaarApp> createState() => _ClickBazaarAppState();
}

class _ClickBazaarAppState extends State<ClickBazaarApp> {
  final GetStorage storage = di.di();
  var sm = SessionManager();
  String lan = "uz";
  bool _shouldRequestPermission = false;
  late final AppLifecycleListener _listener;
  var _subscription;

  @override
  void initState() {
    storage.write(baseUrlPref, AppEnvironment.baseApiUrl);
    _permissionHandler();
    if (Platform.isAndroid) _initWatcher();
    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );
    super.initState();
  }

  _permissionHandler() =>
      AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
        if (!isAllowed) {
          // This is just a basic example. For real apps, you must show some
          // friendly dialog box before call the request method.
          // This is very important to not harm the user experience
          AwesomeNotifications().requestPermissionToSendNotifications();
        }
      });

  _initWatcher() {
    ///***** If time set AUTOMATIC in the device, it can sink an event any time *****///

    if (_subscription != null) {
      _subscription.cancel();
    }
    var id = storage.read('id');
    bool isServerApproved = storage.read(server_approved) ?? false;
    bool isLocalApproved = storage.read(local_approved) ?? false;

    if (id != null && isServerApproved && isLocalApproved) {
      // TODO: TimeChangeDetector needs to be implemented or imported
      // _controller ??= TimeChangeDetector.init;
      // _subscription = _controller?.listen((event) {
      //   var now = DateTime.now();
      //
      //   final DateFormat formatterDate = DateFormat('dd-MM-yyyy HH:mm');
      //   if (!(now.hour == 0 && now.minute == 0)) {
      //     storage.write(is_time_correct, false);
      //     var lang = storage.read(language_pref);
      //     _message =
      //     '${lang == 'uz' ? EVENT_MESSAGE_SUCCESS_UZ : lang == 'ru'
      //         ? EVENT_MESSAGE_SUCCESS_RU
      //         : EVENT_MESSAGE_SUCCESS_QQ}: ${formatterDate.format(now)}';
      //     createNotification(_message);
      //     print(_message);
      //   } else {
      //     print(now.toString());
      //   }
      // },
      //     onError: (error) => print('$ERROR: $error'),
      //     onDone: () => print(STREAM_COMPLETE));
    }
  }

  @override
  void dispose() {
    _listener.dispose();
    super.dispose();
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  void _onDetached() => print('detached');

  void _onResumed() async {
    if (_shouldRequestPermission) {
      _shouldRequestPermission = false;

      var id = JwtDecoder.getUserId(storage.read(TOKEN));

      bool isServerApproved = storage.read(server_approved) ?? false;
      bool isLocalApproved = storage.read(local_approved) ?? false;

      if (id != null && isServerApproved && isLocalApproved) {
        if (Platform.isAndroid) _initWatcher();

        var isLive = await sm.get(functional_live) ?? false;
        print('IS FUNCTIONAL LIVE: $isLive');
        if (!isLive) {
          await determinePosition();
        }
        var time = storage.read(is_time_correct) ?? false;
        var location = storage.read(is_gps_active) ?? false;
        var mockLocation = storage.read(is_not_mocked) ?? false;

        var unlocked = (location && mockLocation && time);

        print('RESUMED ----- APP');

        print('Time approved: $time\n');
        print('GPS approved: $location\n');
        print('MOCK approved: $mockLocation\n');

        if (!unlocked && !isLive) {
          final navigator = NavigationService.navigatorKey.currentState;
          if (navigator != null) {
            navigator.pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => FunctionalLockPage()),
              (route) => false,
            );
          }
        }
      }
    }
  }

  void _onInactive() {
    _shouldRequestPermission = true;
  }

  void _onHidden() => print('hidden');

  void _onPaused() {
    _shouldRequestPermission = true;
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(390, 844),
      minTextAdapt: true,
      splitScreenMode: true,
      // Use builder only if you need to use library outside ScreenUtilInit context
      builder: (_, child) {
        return MaterialApp(
          title: 'Click Bazaar',
          debugShowCheckedModeBanner: false,
          navigatorKey: NavigationService.navigatorKey,
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeManager().themeMode,
          locale: context.locale,
          supportedLocales: context.supportedLocales,
          localizationsDelegates: context.localizationDelegates,
          home: const AuthWrapper(),
        );
      },
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  late final AuthService _authService;
  late final GetStorage _storage;
  bool _isLoading = true;
  bool _isAuthenticated = false;
  UserRole? _userRole;

  @override
  void initState() {
    super.initState();
    _authService = AuthService(
      dio: di.di(),
      storage: di.di(),
      networkInfo: di.di(),
    );
    _storage = di.di<GetStorage>();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    await Future.delayed(const Duration(
        milliseconds:
            1650)); // Splash delay for animation (500ms delay + 1200ms animation - 50ms buffer)

    final token = _storage.read(TOKEN);
    final isDemoMode = _storage.read(is_demo) ?? false;
    bool isAuthenticated = false;
    UserRole? userRole;

    if (token != null) {
      // Check if token is expired
      if (!JwtDecoder.isTokenExpired(token)) {
        isAuthenticated = true;
        // Get user role from storage or decode from token
        final storedRole = _storage.read(USER_ROLE);
        if (storedRole != null) {
          userRole = storedRole == 'supervisor'
              ? UserRole.nazoratchi
              : UserRole.sotuvchi;
        } else {
          // Fallback: decode from token
          final roleFromToken = JwtDecoder.getUserRole(token);
          if (roleFromToken != null) {
            userRole = roleFromToken == 'supervisor'
                ? UserRole.nazoratchi
                : UserRole.sotuvchi;
            // Store role for future use
            await _storage.write(USER_ROLE, roleFromToken);
          }
        }

        // Load user profile if authenticated
        if (isAuthenticated) {
          await _loadUserProfile(token);
        }
      } else {
        // Token expired, clear all storage using LogoutService
        final logoutService = LogoutService(storage: _storage);
        await logoutService.performCompleteLogout();
      }
    } else if (isDemoMode) {
      // Demo mode without stored token - still considered authenticated
      isAuthenticated = true;
      // Get user role from storage or decode from guest token
      final storedRole = _storage.read(USER_ROLE);
      if (storedRole != null) {
        userRole = storedRole == 'supervisor'
            ? UserRole.nazoratchi
            : UserRole.sotuvchi;
      } else {
        // Fallback: decode from guest token
        final roleFromToken = JwtDecoder.getUserRole(GUEST_TOKEN);
        if (roleFromToken != null) {
          userRole = roleFromToken == 'supervisor'
              ? UserRole.nazoratchi
              : UserRole.sotuvchi;
          // Store role for future use
          await _storage.write(USER_ROLE, roleFromToken);
        }
      }

      // Load user profile using guest token
      if (isAuthenticated) {
        await _loadUserProfile(GUEST_TOKEN);
      }
    }

    setState(() {
      _isAuthenticated = isAuthenticated;
      _userRole = userRole;
      _isLoading = false;
    });
  }

  /// Load user profile data
  Future<void> _loadUserProfile(String token) async {
    try {
      final userId = JwtDecoder.getUserId(token);
      if (userId != null) {
        final nazProfileBloc = di.di<NazProfileBloc>();
        nazProfileBloc.add(LoadNazProfileEvent(userId: userId));
      }
    } catch (e) {
      print('Error loading user profile: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SplashScreen();
    }

    if (_isAuthenticated) {
      return LockProvider();
    }

    return const LoginPage();
  }
}
